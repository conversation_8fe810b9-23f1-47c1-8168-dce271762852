import { AppModule } from "./app.module";
import { LoggerService } from "./shared/Providers/logger.service";
import configuration from "./shared/config/app.config";

import { ConfigService } from "@nestjs/config";
import { NestFactory } from "@nestjs/core";

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = app.get(LoggerService);

  const port = configService.get<number>("PORT");
  app.use("/live", (req: any, res: any) => {
    res.status(200).send("Server is up and running");
  });
  await app.listen(port);

  logger.info(`Server running on http://localhost:${port}`);
}

void configuration().then(async () => {
  await bootstrap();
});
