import { EventTrackingService } from "./event-tracking.service";

import { QueueModule } from "../queue/queue.module";
import { Web3Instance } from "../../blockchain/web3Instance";
import { MulticallService } from "../helper/multicall.service";
import { Users, UsersSchema } from "../../database/schemas/users.schema";
import { Tokens, TokensSchema } from "../../database/schemas/tokens.schema";
import { Activity, ActivitySchema } from "../../database/schemas/activity.schema";
import { BlockData, BlockDataSchema } from "../../database/schemas/block-data.schema";
import { Collections, CollectionsSchema } from "../../database/schemas/collections.schema";
import { StatisticsUpdateService } from "../../shared/Providers/statistics-update.service";

import { Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { CacheModule } from "@nestjs/cache-manager";
import * as redisStore from "cache-manager-redis-store";
import { ConfigModule, ConfigService } from "@nestjs/config";

@Module({
  providers: [EventTrackingService, Web3Instance, StatisticsUpdateService, MulticallService],
  imports: [
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      isGlobal: true,
      useFactory: (configService: ConfigService) => ({
        store: redisStore as any,
        host: configService.get("REDIS_HOST"),
        port: configService.get("REDIS_PORT"),
        password: configService.get("REDIS_PASSWORD"),
        username: configService.get("REDIS_USERNAME"),
      }),
    }),
    MongooseModule.forFeature([
      { name: Collections.name, schema: CollectionsSchema },
      { name: Activity.name, schema: ActivitySchema },
      { name: Users.name, schema: UsersSchema },
      { name: Tokens.name, schema: TokensSchema },
      { name: BlockData.name, schema: BlockDataSchema },
    ]),
    QueueModule,
  ],
})
export class EventTrackingModule {}
