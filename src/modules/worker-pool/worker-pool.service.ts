import { Injectable, Logger } from '@nestjs/common';
import { Worker } from 'worker_threads';
import * as path from 'path';
import * as os from 'os';

interface WorkerTask {
  blockStart: number;
  blockSize: number;
  workerId: number;
  dbUri: string;
}

interface WorkerResult {
  workerId: number;
  collections: any[];
  tokens: any[];
  blockNumber: number;
  error?: string;
}

interface ActiveWorker {
  worker: Worker;
  workerId: number;
  startTime: Date;
  blockStart: number;
  timeout?: NodeJS.Timeout;
}

@Injectable()
export class WorkerPoolService {
  private readonly logger = new Logger(WorkerPoolService.name);
  private activeWorkers = new Map<number, ActiveWorker>();
  private readonly WORKER_TIMEOUT = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_MEMORY_PER_WORKER = 1024; // 1GB in MB

  async executeTask(task: WorkerTask): Promise<WorkerResult> {
    return new Promise((resolve, reject) => {
      const { blockStart, blockSize, workerId, dbUri } = task;
      
      // Check system resources before starting worker
      if (!this.checkSystemResources()) {
        reject(new Error('Insufficient system resources to start worker'));
        return;
      }

      const worker = new Worker(path.resolve(__dirname, '../event-tracking/worker.js'), {
        workerData: { blockStart, blockSize, workerId, dbUri },
        resourceLimits: {
          maxOldGenerationSizeMb: this.MAX_MEMORY_PER_WORKER,
          maxYoungGenerationSizeMb: 256,
          codeRangeSizeMb: 128,
        },
      });

      const activeWorker: ActiveWorker = {
        worker,
        workerId,
        startTime: new Date(),
        blockStart,
      };

      this.activeWorkers.set(workerId, activeWorker);

      let isCompleted = false;

      const cleanup = () => {
        if (isCompleted) return;
        isCompleted = true;

        if (activeWorker.timeout) {
          clearTimeout(activeWorker.timeout);
        }
        
        this.activeWorkers.delete(workerId);
        
        // Log worker completion
        const duration = Date.now() - activeWorker.startTime.getTime();
        this.logger.log(`Worker ${workerId} completed in ${Math.round(duration / 1000)}s`);
      };

      const handleTimeout = () => {
        if (isCompleted) return;

        const memUsage = process.memoryUsage();
        this.logger.error(
          `Worker ${workerId} timeout - Memory: ${Math.round(memUsage.rss / 1024 / 1024)}MB RSS, ` +
          `processing block ${blockStart}`
        );

        worker.terminate()
          .then(() => {
            this.logger.warn(`Worker ${workerId} terminated due to timeout`);
          })
          .catch(error => {
            this.logger.error(`Failed to terminate worker ${workerId}: ${error.message}`);
          })
          .finally(() => {
            cleanup();
            reject(new Error(`Worker ${workerId} timed out after ${this.WORKER_TIMEOUT / 1000}s`));
          });
      };

      // Set timeout
      activeWorker.timeout = setTimeout(handleTimeout, this.WORKER_TIMEOUT);

      // Handle worker messages
      worker.on('message', (data: WorkerResult) => {
        if (isCompleted) return;

        cleanup();

        if (data.error) {
          this.logger.error(`Worker ${workerId} reported error: ${data.error}`);
          reject(new Error(`Worker ${workerId} failed: ${data.error}`));
          return;
        }

        resolve(data);
      });

      // Handle worker errors
      worker.on('error', (error) => {
        if (isCompleted) return;

        cleanup();
        this.logger.error(`Worker ${workerId} error: ${error.message}`);
        reject(new Error(`Worker ${workerId} error: ${error.message}`));
      });

      // Handle worker exit
      worker.on('exit', (code, signal) => {
        if (isCompleted) return;

        cleanup();

        if (signal === 'SIGSEGV') {
          this.logger.error(`Worker ${workerId} crashed with segmentation fault`);
          reject(new Error(`Worker ${workerId} segmentation fault`));
        } else if (signal === 'SIGABRT') {
          this.logger.error(`Worker ${workerId} aborted`);
          reject(new Error(`Worker ${workerId} aborted`));
        } else if (code !== 0) {
          this.logger.error(`Worker ${workerId} exited with code ${code}`);
          reject(new Error(`Worker ${workerId} exited with code ${code}`));
        }
      });

      this.logger.log(`Started worker ${workerId} for blocks ${blockStart}-${blockStart + blockSize - 1}`);
    });
  }

  private checkSystemResources(): boolean {
    const memUsage = process.memoryUsage();
    const systemMem = {
      free: os.freemem(),
      total: os.totalmem(),
    };

    const memoryUsagePercent = (memUsage.rss / systemMem.total) * 100;
    const systemFreePercent = (systemMem.free / systemMem.total) * 100;

    // Don't start new workers if memory usage is too high
    if (memoryUsagePercent > 80) {
      this.logger.warn(`High memory usage (${memoryUsagePercent.toFixed(1)}%), delaying worker start`);
      return false;
    }

    if (systemFreePercent < 10) {
      this.logger.warn(`Low system memory (${systemFreePercent.toFixed(1)}% free), delaying worker start`);
      return false;
    }

    return true;
  }

  async terminateAllWorkers(): Promise<void> {
    const terminationPromises: Promise<void>[] = [];

    for (const [workerId, activeWorker] of this.activeWorkers) {
      this.logger.warn(`Terminating worker ${workerId}`);
      
      if (activeWorker.timeout) {
        clearTimeout(activeWorker.timeout);
      }

      terminationPromises.push(
        activeWorker.worker.terminate()
          .then(() => {
            this.logger.log(`Worker ${workerId} terminated successfully`);
          })
          .catch(error => {
            this.logger.error(`Failed to terminate worker ${workerId}: ${error.message}`);
          })
      );
    }

    await Promise.allSettled(terminationPromises);
    this.activeWorkers.clear();
  }

  getActiveWorkerCount(): number {
    return this.activeWorkers.size;
  }

  getActiveWorkerInfo(): Array<{workerId: number; startTime: Date; blockStart: number; duration: number}> {
    const now = Date.now();
    return Array.from(this.activeWorkers.values()).map(worker => ({
      workerId: worker.workerId,
      startTime: worker.startTime,
      blockStart: worker.blockStart,
      duration: now - worker.startTime.getTime(),
    }));
  }

  async waitForCompletion(): Promise<void> {
    while (this.activeWorkers.size > 0) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
}
