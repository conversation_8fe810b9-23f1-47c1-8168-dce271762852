import { App<PERSON><PERSON>roller } from "./app.controller";
import { AppService } from "./app.service";
import { CollectionStatsCronModule } from "./modules/collection-stats-cron/collection-stats-cron.module";
import { EventTrackingModule } from "./modules/event-tracking/event-tracking.module";
import { ElasticsearchModule } from "./modules/elasticsearch/elasticsearch.module";
import { ElasticsearchCronModule } from "./modules/elasticsearch-cron/elasticsearch-cron.module";
import { AuctionModule } from "./modules/auction/auction.module";
import { LoggerService } from "./shared/Providers/logger.service";
import { QueueModule } from "./modules/queue/queue.module";
import { Module } from "@nestjs/common";
import { CacheModule, CacheModuleOptions } from "@nestjs/cache-manager";
import { ScheduleModule } from "@nestjs/schedule";
import { WinstonModule, utilities as nestWinstonModuleUtilities } from "nest-winston";
import * as winston from "winston";
import { MongooseModule } from "@nestjs/mongoose";
import { ConfigModule, ConfigService } from "@nestjs/config";

import moment from "moment";
import * as redisStore from "cache-manager-redis-store";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    CacheModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      isGlobal: true,
      useFactory: (configService: ConfigService): CacheModuleOptions => {
        return {
          store: redisStore,
          host: configService.get("REDIS_HOST"),
          port: configService.get("REDIS_PORT"),
          password: configService.get("REDIS_PASSWORD"),
          username: configService.get("REDIS_USERNAME"),
          ttl: 60 * 60 * 24, // 24 hours in seconds
        } as any;
      },
    }),
    ScheduleModule.forRoot(),
    WinstonModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: () => ({
        transports: [
          new winston.transports.File({
            filename: `${process.cwd()}/logs/log-${moment().format("YYYY-MM-DD")}.log`,
          }),
          new winston.transports.Console({
            format: winston.format.combine(winston.format.timestamp(), nestWinstonModuleUtilities.format.nestLike()),
          }),
        ],
      }),
      inject: [ConfigService],
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        uri: configService.get("MONGO_DB_URI"),
      }),
    }),
    EventTrackingModule,
    ElasticsearchModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        node: configService.get("ELASTICSEARCH_NODE"),
        auth: {
          username: configService.get("ELASTICSEARCH_USERNAME"),
          password: configService.get("ELASTICSEARCH_PASSWORD"),
        },
        maxRetries: 10,
        requestTimeout: 60000,
      }),
    }),
    ElasticsearchCronModule,
    CollectionStatsCronModule,
    AuctionModule,
    QueueModule,
  ],
  controllers: [AppController],
  providers: [AppService, LoggerService],
})
export class AppModule {}
