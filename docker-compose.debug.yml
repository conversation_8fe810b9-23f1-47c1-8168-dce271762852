version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: pixelpark-app-debug
    env_file:
      - .env
    depends_on:
      - redis
      - mongo
      - elasticsearch
    networks:
      - pixelpark-network
    dns:
      - 8.8.8.8
      - 8.8.4.4
    ports:
      - "3001:3001"
      - "9229:9229"  # Node.js inspector port
    volumes:
      - ./logs:/app/logs
      - ./core-dumps:/tmp/core-dumps
    cap_add:
      - SYS_PTRACE  # Required for debugging tools like gdb
    security_opt:
      - seccomp:unconfined  # Allow debugging
    ulimits:
      core:
        soft: -1
        hard: -1
    environment:
      - NODE_OPTIONS=--expose-gc --inspect=0.0.0.0:9229
      - NODE_ENV=development
    command: >
      sh -c "
        mkdir -p /tmp/core-dumps &&
        echo '/tmp/core-dumps/core.%e.%p.%t' > /proc/sys/kernel/core_pattern &&
        ulimit -c unlimited &&
        npm run start:debug-core
      "